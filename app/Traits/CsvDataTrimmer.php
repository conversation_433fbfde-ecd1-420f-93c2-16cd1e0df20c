<?php

namespace App\Traits;

trait CsvDataTrimmer
{
    /**
     * Trim leading and trailing spaces from a value while preserving internal spaces.
     * 
     * @param mixed $value The value to trim
     * @return mixed The trimmed value (null, empty string, or trimmed string)
     */
    protected function trimCsvValue($value)
    {
        // Return null and empty strings as-is
        if ($value === null || $value === '') {
            return $value;
        }
        
        // Convert to string and trim leading/trailing whitespace
        return trim((string) $value);
    }
    
    /**
     * Trim all string values in an array while preserving the array structure.
     * 
     * @param array $data The array of data to trim
     * @return array The array with all string values trimmed
     */
    protected function trimCsvData(array $data): array
    {
        $trimmed = [];
        
        foreach ($data as $key => $value) {
            $trimmed[$key] = $this->trimCsvValue($value);
        }
        
        return $trimmed;
    }
    
    /**
     * Trim specific fields in an array, leaving other fields unchanged.
     * 
     * @param array $data The array of data
     * @param array $fields The fields to trim
     * @return array The array with specified fields trimmed
     */
    protected function trimCsvFields(array $data, array $fields): array
    {
        foreach ($fields as $field) {
            if (array_key_exists($field, $data)) {
                $data[$field] = $this->trimCsvValue($data[$field]);
            }
        }
        
        return $data;
    }
    
    /**
     * Clean and normalize CSV header values by removing BOM, quotes, and whitespace.
     *
     * @param array $headers The array of header values
     * @return array The cleaned header values
     */
    protected function cleanCsvHeaders(array $headers): array
    {
        return array_map(function($header) {
            // Remove BOM, quotes (including backticks), and various whitespace characters
            $cleaned = trim($header, " \t\n\r\0\x0B\x0C\xEF\xBB\xBF\"'`");
            // Remove any remaining invisible characters
            $cleaned = preg_replace('/[\x00-\x1F\x7F]/', '', $cleaned);
            return $cleaned;
        }, $headers);
    }
}
