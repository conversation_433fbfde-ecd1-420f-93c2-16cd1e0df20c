<?php

namespace App\Services;

use App\Models\SiteSetting;
use Illuminate\Support\Facades\Cache;

class CompatibilityColumnService
{
    /**
     * Get the compatibility columns configuration.
     */
    public function getColumnConfiguration(bool $adminMode = false): array
    {
        $cacheKey = 'compatibility_columns_config';
        
        $config = Cache::remember($cacheKey, 3600, function () {
            return SiteSetting::get('parts_compatibility_columns', $this->getDefaultConfiguration());
        });

        // For admin mode, return all columns
        if ($adminMode) {
            return $config;
        }

        // For public mode, filter to only enabled columns
        return array_filter($config, function ($column) {
            return $column['enabled'] ?? false;
        });
    }

    /**
     * Get visible columns sorted by order.
     */
    public function getVisibleColumns(bool $adminMode = false): array
    {
        $config = $this->getColumnConfiguration($adminMode);

        // For admin mode, ensure compatible column is enabled
        if ($adminMode && isset($config['compatible'])) {
            $config['compatible']['enabled'] = true;
        }

        // Sort by order
        uasort($config, function ($a, $b) {
            return ($a['order'] ?? 999) <=> ($b['order'] ?? 999);
        });

        return $config;
    }

    /**
     * Get column value from model/part data.
     */
    public function getColumnValue(string $columnKey, array $columnConfig, $model, $part): string
    {
        $source = $columnConfig['source'] ?? '';

        switch ($source) {
            case 'model.brand.name':
                return $model['brand']['name'] ?? '-';

            case 'model.name':
                return $model['name'] ?? '-';

            case 'model.model_number':
                return $model['model_number'] ?? '-';

            case 'part.name':
                return $part['name'] ?? '-';

            case 'part.part_number':
                return $part['part_number'] ?? '-';

            case 'part.manufacturer':
                return $part['manufacturer'] ?? '-';

            case 'part.description':
                return $part['description'] ?? '-';

            case 'part.category.name':
                return $part['category']['name'] ?? '-';

            case 'model.pivot.compatibility_notes':
                return $model['pivot']['compatibility_notes'] ?? '-';

            case 'model.pivot.display_type':
                return $model['pivot']['display_type'] ?? '-';

            case 'model.pivot.display_size':
                return $model['pivot']['display_size'] ?? '-';

            case 'model.pivot.location':
                return $model['pivot']['location'] ?? '-';

            case 'model.pivot.is_compatible':
                $value = $model['pivot']['is_compatible'] ?? true; // Default to compatible if not specified
                return is_bool($value) ? $value : (bool) $value;

            case 'model.pivot.is_verified':
                $value = $model['pivot']['is_verified'] ?? false;
                return is_bool($value) ? $value : (bool) $value;

            case 'model.pivot.front_camera_type':
                return $model['pivot']['front_camera_type'] ?? '-';

            case 'model.pivot.camera_position':
                return $model['pivot']['camera_position'] ?? '-';

            case 'model.pivot.battery_mah':
                return $model['pivot']['battery_mah'] ?? '-';

            case 'model.pivot.pin_model':
                return $model['pivot']['pin_model'] ?? '-';

            case 'model.pivot.connector_types':
                return $model['pivot']['connector_types'] ?? '-';

            case 'model.pivot.types':
                return $model['pivot']['types'] ?? '-';

            case 'model.pivot.additional_info':
                return $model['pivot']['additional_info'] ?? '-';

            default:
                return '-';
        }
    }

    /**
     * Get responsive CSS class for column.
     */
    public function getResponsiveClass(array $columnConfig): string
    {
        $breakpoint = $columnConfig['minBreakpoint'] ?? 'xs';
        
        switch ($breakpoint) {
            case 'sm':
                return 'hidden sm:table-cell';
            case 'md':
                return 'hidden md:table-cell';
            case 'lg':
                return 'hidden lg:table-cell';
            case 'xl':
                return 'hidden xl:table-cell';
            case 'xs':
            default:
                return '';
        }
    }

    /**
     * Update column configuration.
     */
    public function updateColumnConfiguration(array $config): bool
    {
        $result = SiteSetting::set('parts_compatibility_columns', $config, 'json',
            'Configuration for parts compatibility table columns', 'parts_management');

        // Clear cache
        Cache::forget('compatibility_columns_config');

        return $result !== null;
    }

    /**
     * Validate column configuration.
     */
    public function validateConfiguration(array $config): array
    {
        $errors = [];

        // Check each required column individually
        foreach ($config as $columnKey => $column) {
            if (($column['required'] ?? false) && !($column['enabled'] ?? false)) {
                $errors[] = "Required column '{$columnKey}' must be enabled";
            }
        }

        // Also check if at least one required column is enabled (fallback)
        if (empty($errors)) {
            $requiredColumnsEnabled = false;
            foreach ($config as $column) {
                if (($column['required'] ?? false) && ($column['enabled'] ?? false)) {
                    $requiredColumnsEnabled = true;
                    break;
                }
            }

            if (!$requiredColumnsEnabled) {
                $errors[] = 'At least one required column must be enabled';
            }
        }

        return $errors;
    }

    /**
     * Get default column configuration.
     */
    private function getDefaultConfiguration(): array
    {
        return [
            'brand' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Brand',
                'source' => 'model.brand.name',
                'priority' => 1,
                'minBreakpoint' => 'xs',
                'width' => '120px',
                'minWidth' => '100px',
                'maxWidth' => '150px',
                'truncate' => true
            ],
            'model' => [
                'enabled' => true,
                'required' => true,
                'order' => 2,
                'label' => 'Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs',
                'width' => '180px',
                'minWidth' => '150px',
                'maxWidth' => '250px',
                'truncate' => true
            ],
            'model_number' => [
                'enabled' => false,
                'required' => false,
                'order' => 3,
                'label' => 'Model Number',
                'source' => 'model.model_number',
                'priority' => 3,
                'minBreakpoint' => 'sm',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'part_name' => [
                'enabled' => false,
                'required' => false,
                'order' => 4,
                'label' => 'Part Name',
                'source' => 'part.name',
                'priority' => 3,
                'minBreakpoint' => 'sm',
                'width' => '180px',
                'minWidth' => '150px',
                'maxWidth' => '250px',
                'truncate' => true
            ],
            'part_number' => [
                'enabled' => false,
                'required' => false,
                'order' => 5,
                'label' => 'Part Number',
                'source' => 'part.part_number',
                'priority' => 4,
                'minBreakpoint' => 'sm',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'description' => [
                'enabled' => false,
                'required' => false,
                'order' => 6,
                'label' => 'Description',
                'source' => 'part.description',
                'priority' => 5,
                'minBreakpoint' => 'md',
                'width' => '200px',
                'minWidth' => '150px',
                'maxWidth' => '300px',
                'truncate' => true
            ],
            'manufacturer' => [
                'enabled' => false,
                'required' => false,
                'order' => 7,
                'label' => 'Manufacturer',
                'source' => 'part.manufacturer',
                'priority' => 5,
                'minBreakpoint' => 'md',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'category' => [
                'enabled' => false,
                'required' => false,
                'order' => 8,
                'label' => 'Category',
                'source' => 'part.category.name',
                'priority' => 5,
                'minBreakpoint' => 'md',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'display_type' => [
                'enabled' => false,
                'required' => false,
                'order' => 9,
                'label' => 'Display Type',
                'source' => 'model.pivot.display_type',
                'priority' => 6,
                'minBreakpoint' => 'lg',
                'width' => '130px',
                'minWidth' => '110px',
                'maxWidth' => '180px',
                'truncate' => true
            ],
            'display_size' => [
                'enabled' => false,
                'required' => false,
                'order' => 10,
                'label' => 'Display Size',
                'source' => 'model.pivot.display_size',
                'priority' => 6,
                'minBreakpoint' => 'lg',
                'width' => '130px',
                'minWidth' => '110px',
                'maxWidth' => '180px',
                'truncate' => true
            ],
            'location' => [
                'enabled' => false,
                'required' => false,
                'order' => 11,
                'label' => 'Location',
                'source' => 'model.pivot.location',
                'priority' => 7,
                'minBreakpoint' => 'lg',
                'width' => '130px',
                'minWidth' => '110px',
                'maxWidth' => '180px',
                'truncate' => true
            ],
            'front_camera_type' => [
                'enabled' => false,
                'required' => false,
                'order' => 12,
                'label' => 'Front Camera Type',
                'source' => 'model.pivot.front_camera_type',
                'priority' => 7,
                'minBreakpoint' => 'lg',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'camera_position' => [
                'enabled' => false,
                'required' => false,
                'order' => 13,
                'label' => 'Camera Position',
                'source' => 'model.pivot.camera_position',
                'priority' => 7,
                'minBreakpoint' => 'lg',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'battery_mah' => [
                'enabled' => false,
                'required' => false,
                'order' => 14,
                'label' => 'Battery mAh',
                'source' => 'model.pivot.battery_mah',
                'priority' => 7,
                'minBreakpoint' => 'lg',
                'width' => '130px',
                'minWidth' => '110px',
                'maxWidth' => '180px',
                'truncate' => true
            ],
            'pin_model' => [
                'enabled' => false,
                'required' => false,
                'order' => 15,
                'label' => 'Pin & Model',
                'source' => 'model.pivot.pin_model',
                'priority' => 7,
                'minBreakpoint' => 'lg',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'connector_types' => [
                'enabled' => false,
                'required' => false,
                'order' => 16,
                'label' => 'Connector Types',
                'source' => 'model.pivot.connector_types',
                'priority' => 7,
                'minBreakpoint' => 'lg',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'types' => [
                'enabled' => false,
                'required' => false,
                'order' => 17,
                'label' => 'Types',
                'source' => 'model.pivot.types',
                'priority' => 7,
                'minBreakpoint' => 'lg',
                'width' => '130px',
                'minWidth' => '110px',
                'maxWidth' => '180px',
                'truncate' => true
            ],
            'compatible' => [
                'enabled' => false,
                'required' => false,
                'order' => 18,
                'label' => 'Compatible',
                'source' => 'model.pivot.is_compatible',
                'priority' => 2,
                'minBreakpoint' => 'xs',
                'width' => '110px',
                'minWidth' => '100px',
                'maxWidth' => '130px',
                'truncate' => false
            ],
            'notes' => [
                'enabled' => false,
                'required' => false,
                'order' => 19,
                'label' => 'Notes',
                'source' => 'model.pivot.compatibility_notes',
                'priority' => 8,
                'minBreakpoint' => 'xl',
                'width' => '200px',
                'minWidth' => '150px',
                'maxWidth' => '300px',
                'truncate' => true
            ],
            'additional_info' => [
                'enabled' => false,
                'required' => false,
                'order' => 20,
                'label' => 'Additional Info',
                'source' => 'model.pivot.additional_info',
                'priority' => 8,
                'minBreakpoint' => 'xl',
                'width' => '200px',
                'minWidth' => '150px',
                'maxWidth' => '300px',
                'truncate' => true
            ],
            'verified' => [
                'enabled' => true,
                'required' => false,
                'order' => 21,
                'label' => 'Status',
                'source' => 'model.pivot.is_verified',
                'priority' => 2,
                'minBreakpoint' => 'xs',
                'width' => '100px',
                'minWidth' => '90px',
                'maxWidth' => '120px',
                'truncate' => false
            ]
        ];
    }

    /**
     * Auto-enable columns that have data during CSV import.
     */
    public function autoEnableColumnsWithData(array $importedColumns): bool
    {
        $config = $this->getColumnConfiguration(true); // Get all columns
        $hasChanges = false;

        // Map of CSV column names to config keys
        $columnMapping = [
            'model_number' => 'model_number',
            'part_name' => 'part_name',
            'part_number' => 'part_number',
            'description' => 'description',
            'manufacturer' => 'manufacturer',
            'category' => 'category',
            'display_type' => 'display_type',
            'display_size' => 'display_size',
            'location' => 'location',
            'front_camera_type' => 'front_camera_type',
            'camera_position' => 'camera_position',
            'battery_mah' => 'battery_mah',
            'pin_model' => 'pin_model',
            'connector_types' => 'connector_types',
            'types' => 'types',
            'compatible' => 'compatible',
            'notes' => 'notes',
            'additional_info' => 'additional_info',
        ];

        foreach ($importedColumns as $csvColumn) {
            $configKey = $columnMapping[$csvColumn] ?? null;
            if ($configKey && isset($config[$configKey]) && !$config[$configKey]['enabled']) {
                $config[$configKey]['enabled'] = true;
                $hasChanges = true;
            }
        }

        if ($hasChanges) {
            return $this->updateColumnConfiguration($config);
        }

        return true;
    }
}
