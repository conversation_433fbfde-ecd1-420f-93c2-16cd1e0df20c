<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class SiteSettingsController extends Controller
{
    /**
     * Display the site settings management page.
     */
    public function index()
    {
        $settings = SiteSetting::where('is_active', true)
            ->orderBy('category')
            ->orderBy('key')
            ->get()
            ->groupBy('category');

        // Ensure all default categories exist
        $defaults = SiteSetting::getDefaults();
        $defaultsByCategory = collect($defaults)->groupBy(function ($item) {
            return $item['category'];
        });

        foreach ($defaultsByCategory as $category => $categoryDefaults) {
            if (!$settings->has($category)) {
                $settings[$category] = collect();
            }
        }

        // Get active menus for navbar and footer selection
        $menus = \App\Models\Menu::active()
            ->select('id', 'name', 'location', 'description')
            ->orderBy('name')
            ->get();

        return Inertia::render('admin/SiteSettings/Index', [
            'settings' => $settings,
            'categories' => $settings->keys(),
            'menus' => $menus,
        ]);
    }

    /**
     * Update site settings.
     */
    public function update(Request $request)
    {
        // Handle both direct form data and nested settings array
        $settingsData = $request->has('settings') ? $request->settings : $request->all();

        // If it's direct form data, convert to settings array format
        if (!$request->has('settings')) {
            $settingsArray = [];
            $defaults = SiteSetting::getDefaults();

            foreach ($settingsData as $key => $value) {
                if (isset($defaults[$key])) {
                    $settingsArray[] = [
                        'key' => $key,
                        'value' => $value,
                        'type' => $defaults[$key]['type']
                    ];
                } else {
                    // Log missing keys for debugging
                    \Log::info("Setting key not found in defaults: {$key}");
                }
            }
            $settingsData = $settingsArray;
        }

        $validator = Validator::make(['settings' => $settingsData], [
            'settings' => 'required|array',
            'settings.*.key' => 'required|string',
            'settings.*.value' => 'nullable',
            'settings.*.type' => 'required|string|in:string,text,boolean,integer,array,json',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            foreach ($settingsData as $settingData) {
                $key = $settingData['key'];
                $value = $settingData['value'];
                $type = $settingData['type'];

                // Type casting based on setting type
                switch ($type) {
                    case 'boolean':
                        $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                        break;
                    case 'integer':
                        $value = (int) $value;
                        break;
                    case 'array':
                    case 'json':
                        if (is_string($value)) {
                            $value = json_decode($value, true) ?? [];
                        }
                        break;
                    case 'string':
                    case 'text':
                    default:
                        $value = (string) $value;
                }

                $setting = SiteSetting::where('key', $key)->first();

                if ($setting) {
                    $setting->update(['value' => $value]);
                } else {
                    // Create new setting with defaults
                    $defaults = SiteSetting::getDefaults();
                    $defaultConfig = $defaults[$key] ?? [];

                    SiteSetting::create([
                        'key' => $key,
                        'value' => $value,
                        'type' => $type,
                        'description' => $defaultConfig['description'] ?? '',
                        'category' => $defaultConfig['category'] ?? 'general',
                        'is_active' => true,
                    ]);
                }
            }

            return back()->with('success', 'Site settings updated successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update site settings: ' . $e->getMessage());
        }
    }

    /**
     * Reset settings to defaults.
     */
    public function reset(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        try {
            $category = $request->category;
            
            if ($category) {
                // Reset specific category
                SiteSetting::where('category', $category)->delete();
                
                $defaults = SiteSetting::getDefaults();
                foreach ($defaults as $key => $config) {
                    if ($config['category'] === $category) {
                        SiteSetting::updateOrCreate(
                            ['key' => $key],
                            [
                                'value' => $config['value'],
                                'type' => $config['type'],
                                'description' => $config['description'],
                                'category' => $config['category'],
                                'is_active' => true,
                            ]
                        );
                    }
                }
                
                $message = "Settings for '{$category}' category reset to defaults.";
            } else {
                // Reset all settings
                SiteSetting::truncate();
                SiteSetting::seedDefaults();
                $message = 'All site settings reset to defaults.';
            }

            return back()->with('success', $message);
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to reset settings: ' . $e->getMessage());
        }
    }

    /**
     * Get settings for API consumption.
     */
    public function api()
    {
        $settings = SiteSetting::where('is_active', true)
            ->pluck('value', 'key');

        return response()->json($settings);
    }

    /**
     * Get settings by category for API consumption.
     */
    public function apiByCategory(string $category)
    {
        $settings = SiteSetting::getByCategory($category);

        return response()->json($settings);
    }

    /**
     * Get public branding settings (accessible without admin privileges).
     */
    public function publicBranding()
    {
        $brandingSettings = SiteSetting::where('is_active', true)
            ->where('category', 'branding')
            ->pluck('value', 'key');

        return response()->json($brandingSettings);
    }

    /**
     * Get footer configuration for public consumption.
     */
    public function footerConfig()
    {
        $footerSettings = SiteSetting::getByCategory('footer');

        // Ensure all required settings exist with defaults
        $defaults = SiteSetting::getDefaults();
        foreach ($defaults as $key => $config) {
            if ($config['category'] === 'footer' && !isset($footerSettings[$key])) {
                $footerSettings[$key] = $config['value'];
            }
        }

        // If footer has menus selected, include menu items
        if (!empty($footerSettings['footer_menu_ids']) && is_array($footerSettings['footer_menu_ids'])) {
            $menus = \App\Models\Menu::with(['rootItems.children' => function ($query) {
                $query->where('is_active', true)->orderBy('order');
            }])->whereIn('id', $footerSettings['footer_menu_ids'])
              ->where('is_active', true)
              ->get();

            $footerSettings['footer_menus'] = $menus;
        } else {
            $footerSettings['footer_menus'] = [];
        }

        return response()->json($footerSettings);
    }

    /**
     * Get navbar configuration for public consumption.
     */
    public function navbarConfig()
    {
        try {
            $navbarSettings = SiteSetting::getByCategory('navbar');

            // Ensure all required settings exist with defaults
            $defaults = SiteSetting::getDefaults();
            foreach ($defaults as $key => $config) {
                if ($config['category'] === 'navbar' && !isset($navbarSettings[$key])) {
                    $navbarSettings[$key] = $config['value'];
                }
            }

        // If navbar has a menu selected, include menu items
        if (!empty($navbarSettings['navbar_menu_id'])) {
            $menu = \App\Models\Menu::with(['rootItems.children' => function ($query) {
                $query->where('is_active', true)->orderBy('order');
            }])->find($navbarSettings['navbar_menu_id']);

            if ($menu && $menu->rootItems) {
                $navbarSettings['menu_items'] = $menu->rootItems->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'title' => $item->title,
                        'url' => $item->computed_url ?: $item->url ?: '#',
                        'target' => $item->target ?: '_self',
                        'children' => $item->children->map(function ($child) {
                            return [
                                'id' => $child->id,
                                'title' => $child->title,
                                'url' => $child->computed_url ?: $child->url ?: '#',
                                'target' => $child->target ?: '_self',
                                'children' => [], // Limit to 2 levels for navbar
                            ];
                        })->toArray(),
                    ];
                })->toArray();
            } else {
                $navbarSettings['menu_items'] = [];
            }
        } else {
            $navbarSettings['menu_items'] = [];
        }

            return response()->json($navbarSettings);
        } catch (\Exception $e) {
            \Log::error('Error fetching navbar configuration', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'timestamp' => now()
            ]);

            // Return safe defaults on error
            return response()->json([
                'navbar_enabled' => true,
                'navbar_menu_id' => null,
                'navbar_background_color' => '#ffffff',
                'navbar_text_color' => '#1f2937',
                'navbar_logo_position' => 'left',
                'navbar_show_search' => true,
                'navbar_sticky' => true,
                'navbar_style' => 'default',
                'menu_items' => [],
            ]);
        }
    }

    /**
     * Get contact configuration for public consumption.
     */
    public function contactConfig()
    {
        try {
            $contactSettings = SiteSetting::getByCategory('contact');

            // Ensure all required settings exist with defaults
            $defaults = SiteSetting::getDefaults();
            foreach ($defaults as $key => $config) {
                if ($config['category'] === 'contact' && !isset($contactSettings[$key])) {
                    $contactSettings[$key] = $config['value'];
                }
            }

            return response()->json($contactSettings);
        } catch (\Exception $e) {
            \Log::error('Error fetching contact configuration', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'timestamp' => now()
            ]);

            // Return safe defaults on error
            return response()->json([
                'contact_dropdown_enabled' => true,
                'contact_whatsapp_enabled' => false,
                'contact_whatsapp_number' => '',
                'contact_whatsapp_message' => 'Hello! I need help with mobile parts.',
                'contact_telegram_enabled' => false,
                'contact_telegram_username' => '',
                'contact_messenger_enabled' => false,
                'contact_messenger_link' => '',
                'contact_dropdown_title' => 'Contact us',
            ]);
        }
    }
}
