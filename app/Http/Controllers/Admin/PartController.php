<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Services\CompatibilityColumnService;
use App\Traits\CsvDataTrimmer;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Exception;

class PartController extends Controller
{
    use CsvDataTrimmer;
    public function __construct(
        private CompatibilityColumnService $compatibilityColumnService
    ) {
        //
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Part::with('category')
            ->withCount('models');

        // Apply search
        if ($search = $request->get('search')) {
            $searchType = $request->get('type', 'all');

            $query->where(function ($q) use ($search, $searchType) {
                switch ($searchType) {
                    case 'index':
                        // Exact ID search only
                        if (is_numeric($search)) {
                            $q->where('id', (int) $search);
                        } else {
                            // If not numeric, return no results
                            $q->where('id', -1);
                        }
                        break;
                    case 'part_name':
                        // Search in part-related fields only
                        $q->where('name', 'LIKE', "%{$search}%")
                          ->orWhere('part_number', 'LIKE', "%{$search}%")
                          ->orWhere('manufacturer', 'LIKE', "%{$search}%")
                          ->orWhere('description', 'LIKE', "%{$search}%");
                        break;
                    case 'category':
                        // Search in category only
                        $q->whereHas('category', function ($categoryQuery) use ($search) {
                            $categoryQuery->where('name', 'LIKE', "%{$search}%");
                        });
                        break;
                    default:
                        // Search all fields (existing behavior)
                        $q->where('name', 'LIKE', "%{$search}%")
                          ->orWhere('part_number', 'LIKE', "%{$search}%")
                          ->orWhere('manufacturer', 'LIKE', "%{$search}%")
                          ->orWhere('description', 'LIKE', "%{$search}%")
                          ->orWhereHas('category', function ($categoryQuery) use ($search) {
                              $categoryQuery->where('name', 'LIKE', "%{$search}%");
                          });

                        // Add ID search for exact numeric matches in 'all' search
                        if (is_numeric($search)) {
                            $q->orWhere('id', $search);
                        }
                        break;
                }
            });
        }

        // Apply category filter
        if ($categoryId = $request->get('category_id')) {
            $query->where('category_id', $categoryId);
        }

        // Apply manufacturer filter
        if ($manufacturer = $request->get('manufacturer')) {
            $query->where('manufacturer', 'LIKE', "%{$manufacturer}%");
        }

        // Apply status filter
        if ($request->has('status')) {
            $status = $request->get('status');
            if ($status !== 'all') {
                $query->where('is_active', $status === 'active');
            }
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        $allowedSorts = ['id', 'name', 'part_number', 'manufacturer', 'created_at', 'models_count'];
        if (in_array($sortBy, $allowedSorts)) {
            if ($sortBy === 'models_count') {
                $query->orderBy('models_count', $sortOrder);
            } else {
                $query->orderBy($sortBy, $sortOrder);
            }
        } else {
            $query->orderBy('name', 'asc');
        }

        $parts = $query->paginate(15)->withQueryString();

        // Get filter options
        $categories = Category::active()->orderBy('name')->get(['id', 'name']);
        $manufacturers = Part::select('manufacturer')
            ->whereNotNull('manufacturer')
            ->distinct()
            ->orderBy('manufacturer')
            ->pluck('manufacturer');

        return Inertia::render('admin/Parts/Index', [
            'parts' => $parts,
            'filters' => [
                'categories' => $categories,
                'manufacturers' => $manufacturers,
            ],
            'queryParams' => $request->only(['search', 'category_id', 'manufacturer', 'status', 'sort_by', 'sort_order', 'view']),
            'exportImportEnabled' => \App\Models\SiteSetting::get('admin_parts_export_import_enabled', false),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::active()->orderBy('name')->get();

        return Inertia::render('admin/Parts/Create', [
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:categories,id',
            'name' => 'required|string|max:255',
            'part_number' => 'nullable|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'specifications' => 'nullable|array',
            'images' => 'nullable|array',
            'is_active' => 'boolean',
            'sales_button_name' => 'nullable|string|max:255',
            'sales_button_url' => 'nullable|url|max:500',
        ]);

        Part::create($validated);

        return redirect()->route('admin.parts.index')
            ->with('success', 'Part created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Part $part)
    {
        $part->load('category', 'models.brand');

        // Get compatibility column configuration for admin view (all columns)
        $compatibilityColumns = $this->compatibilityColumnService->getVisibleColumns(true);

        return Inertia::render('admin/Parts/Show', [
            'part' => $part,
            'showVerificationStatus' => \App\Models\SiteSetting::get('parts_show_verification_status', true),
            'compatibilityColumns' => $compatibilityColumns,
            'isAdminView' => true,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Part $part)
    {
        $categories = Category::active()->orderBy('name')->get();

        return Inertia::render('admin/Parts/Edit', [
            'part' => $part,
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Part $part)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:categories,id',
            'name' => 'required|string|max:255',
            'part_number' => 'nullable|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'specifications' => 'nullable|array',
            'images' => 'nullable|array',
            'is_active' => 'boolean',
            'sales_button_name' => 'nullable|string|max:255',
            'sales_button_url' => 'nullable|url|max:500',
        ]);

        $part->update($validated);

        return redirect()->route('admin.parts.index')
            ->with('success', 'Part updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Part $part)
    {
        $part->delete();

        return redirect()->route('admin.parts.index')
            ->with('success', 'Part deleted successfully.');
    }

    /**
     * Manage part compatibility with models.
     */
    public function compatibility(Part $part)
    {
        $part->load('models.brand');
        $availableModels = MobileModel::with('brand')
            ->whereNotIn('id', $part->models->pluck('id'))
            ->active()
            ->orderBy('name')
            ->get();

        // Get compatibility column configuration for admin view (all columns)
        $compatibilityColumns = $this->compatibilityColumnService->getVisibleColumns(true);

        return Inertia::render('admin/Parts/Compatibility', [
            'part' => $part,
            'availableModels' => $availableModels,
            'showVerificationStatus' => \App\Models\SiteSetting::get('parts_show_verification_status', true),
            'compatibilityColumns' => $compatibilityColumns,
            'isAdminView' => true,
        ]);
    }

    /**
     * Edit part compatibility with models in table view.
     */
    public function editCompatibility(Part $part)
    {
        $part->load(['models.brand', 'category']);

        // Get all models with their compatibility status for this part
        $allModels = MobileModel::with('brand')
            ->active()
            ->orderBy('name')
            ->get()
            ->map(function ($model) use ($part) {
                $compatibility = $part->models->firstWhere('id', $model->id);
                return [
                    'id' => $model->id,
                    'name' => $model->name,
                    'model_number' => $model->model_number,
                    'release_year' => $model->release_year,
                    'brand' => $model->brand,
                    'is_compatible' => $compatibility?->pivot?->is_compatible ?? ($compatibility !== null),
                    'compatibility_notes' => $compatibility?->pivot?->compatibility_notes,
                    'is_verified' => $compatibility?->pivot?->is_verified ?? false,
                    'display_type' => $compatibility?->pivot?->display_type,
                    'display_size' => $compatibility?->pivot?->display_size,
                    'location' => $compatibility?->pivot?->location,
                ];
            });

        return Inertia::render('admin/Parts/EditCompatibility', [
            'part' => $part,
            'allModels' => $allModels,
            'showVerificationStatus' => \App\Models\SiteSetting::get('parts_show_verification_status', true),
        ]);
    }

    /**
     * Add model compatibility.
     */
    public function addCompatibility(Request $request, Part $part)
    {
        $validated = $request->validate([
            'model_id' => 'required|exists:models,id',
            'compatibility_notes' => 'nullable|string',
            'is_verified' => 'boolean',
            'display_type' => 'nullable|string|max:255',
            'display_size' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'front_camera_type' => 'nullable|string|max:255',
            'camera_position' => 'nullable|string|max:255',
            'battery_mah' => 'nullable|string|max:255',
            'pin_model' => 'nullable|string|max:255',
            'connector_types' => 'nullable|string|max:255',
            'types' => 'nullable|string|max:255',
            'additional_info' => 'nullable|string',
        ]);

        $part->models()->attach($validated['model_id'], [
            'compatibility_notes' => $validated['compatibility_notes'] ?? null,
            'is_verified' => $validated['is_verified'] ?? false,
            'display_type' => $validated['display_type'] ?? null,
            'display_size' => $validated['display_size'] ?? null,
            'location' => $validated['location'] ?? null,
            'front_camera_type' => $validated['front_camera_type'] ?? null,
            'camera_position' => $validated['camera_position'] ?? null,
            'battery_mah' => $validated['battery_mah'] ?? null,
            'pin_model' => $validated['pin_model'] ?? null,
            'connector_types' => $validated['connector_types'] ?? null,
            'types' => $validated['types'] ?? null,
            'additional_info' => $validated['additional_info'] ?? null,
        ]);

        return back()->with('success', 'Model compatibility added successfully.');
    }

    /**
     * Update model compatibility.
     */
    public function updateCompatibility(Request $request, Part $part, MobileModel $model)
    {
        $validated = $request->validate([
            'compatibility_notes' => 'nullable|string',
            'is_verified' => 'boolean',
            'display_type' => 'nullable|string|max:255',
            'display_size' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'front_camera_type' => 'nullable|string|max:255',
            'camera_position' => 'nullable|string|max:255',
            'battery_mah' => 'nullable|string|max:255',
            'pin_model' => 'nullable|string|max:255',
            'connector_types' => 'nullable|string|max:255',
            'types' => 'nullable|string|max:255',
            'additional_info' => 'nullable|string',
        ]);

        $part->models()->updateExistingPivot($model->id, [
            'compatibility_notes' => $validated['compatibility_notes'] ?? null,
            'is_verified' => $validated['is_verified'] ?? false,
            'display_type' => $validated['display_type'] ?? null,
            'display_size' => $validated['display_size'] ?? null,
            'location' => $validated['location'] ?? null,
            'front_camera_type' => $validated['front_camera_type'] ?? null,
            'camera_position' => $validated['camera_position'] ?? null,
            'battery_mah' => $validated['battery_mah'] ?? null,
            'pin_model' => $validated['pin_model'] ?? null,
            'connector_types' => $validated['connector_types'] ?? null,
            'types' => $validated['types'] ?? null,
            'additional_info' => $validated['additional_info'] ?? null,
        ]);

        return back()->with('success', 'Model compatibility updated successfully.');
    }

    /**
     * Remove model compatibility.
     */
    public function removeCompatibility(Part $part, MobileModel $model)
    {
        $part->models()->detach($model->id);

        return back()->with('success', 'Model compatibility removed successfully.');
    }

    /**
     * Toggle part active status.
     */
    public function toggleStatus(Part $part)
    {
        $part->update([
            'is_active' => !$part->is_active
        ]);

        return response()->json([
            'success' => true,
            'is_active' => $part->is_active,
            'message' => $part->is_active ? 'Part activated successfully.' : 'Part deactivated successfully.'
        ]);
    }

    /**
     * Add bulk model compatibility.
     */
    public function addBulkCompatibility(Request $request, Part $part)
    {
        $validated = $request->validate([
            'model_ids' => 'required|array|min:1',
            'model_ids.*' => 'required|exists:models,id',
            'compatibility_notes' => 'nullable|string',
            'is_verified' => 'boolean',
        ]);

        // Get existing model IDs to avoid duplicates
        $existingModelIds = $part->models()->pluck('models.id')->toArray();

        // Filter out already existing models
        $newModelIds = array_diff($validated['model_ids'], $existingModelIds);

        if (empty($newModelIds)) {
            return back()->withErrors(['model_ids' => 'All selected models are already compatible with this part.']);
        }

        $attachData = [];
        foreach ($newModelIds as $modelId) {
            $attachData[$modelId] = [
                'compatibility_notes' => $validated['compatibility_notes'] ?? null,
                'is_verified' => $validated['is_verified'] ?? false,
            ];
        }

        $part->models()->attach($attachData);

        $count = count($newModelIds);
        $skipped = count($validated['model_ids']) - $count;

        $message = "Added compatibility for {$count} models successfully.";
        if ($skipped > 0) {
            $message .= " ({$skipped} models were already compatible and skipped.)";
        }

        return back()->with('success', $message);
    }

    /**
     * Export parts to CSV.
     */
    public function export(Request $request)
    {
        // Check if export/import functionality is enabled
        $exportEnabled = \App\Models\SiteSetting::get('admin_parts_export_import_enabled', false);
        if (!$exportEnabled) {
            return response()->json(['error' => 'This feature is disabled for security reasons.'], 403);
        }
        $request->validate([
            'ids' => 'nullable|array',
            'ids.*' => 'exists:parts,id',
            'search' => 'nullable|string',
            'category_id' => 'nullable|exists:categories,id',
            'manufacturer' => 'nullable|string',
            'status' => 'nullable|in:active,inactive',
        ]);

        $query = Part::with(['category', 'models.brand']);

        // If specific IDs are provided, filter by them
        if ($request->has('ids') && !empty($request->ids)) {
            $query->whereIn('id', $request->ids);
        } else {
            // Apply the same filters as the index method when exporting all

            // Apply search
            if ($search = $request->get('search')) {
                $searchType = $request->get('type', 'all');

                $query->where(function ($q) use ($search, $searchType) {
                    switch ($searchType) {
                        case 'index':
                            // Exact ID search only
                            if (is_numeric($search)) {
                                $q->where('id', (int) $search);
                            } else {
                                // If not numeric, return no results
                                $q->where('id', -1);
                            }
                            break;
                        case 'part_name':
                            // Search in part-related fields only
                            $q->where('name', 'LIKE', "%{$search}%")
                              ->orWhere('part_number', 'LIKE', "%{$search}%")
                              ->orWhere('manufacturer', 'LIKE', "%{$search}%")
                              ->orWhere('description', 'LIKE', "%{$search}%");
                            break;
                        case 'category':
                            // Search in category only
                            $q->whereHas('category', function ($categoryQuery) use ($search) {
                                $categoryQuery->where('name', 'LIKE', "%{$search}%");
                            });
                            break;
                        default:
                            // Search all fields (existing behavior)
                            $q->where('name', 'LIKE', "%{$search}%")
                              ->orWhere('part_number', 'LIKE', "%{$search}%")
                              ->orWhere('manufacturer', 'LIKE', "%{$search}%")
                              ->orWhere('description', 'LIKE', "%{$search}%")
                              ->orWhereHas('category', function ($categoryQuery) use ($search) {
                                  $categoryQuery->where('name', 'LIKE', "%{$search}%");
                              });
                            break;
                    }
                });
            }

            // Apply category filter
            if ($categoryId = $request->get('category_id')) {
                $query->where('category_id', $categoryId);
            }

            // Apply manufacturer filter
            if ($manufacturer = $request->get('manufacturer')) {
                $query->where('manufacturer', 'LIKE', "%{$manufacturer}%");
            }

            // Apply status filter
            if ($status = $request->get('status')) {
                if ($status === 'active') {
                    $query->where('is_active', true);
                } elseif ($status === 'inactive') {
                    $query->where('is_active', false);
                }
            }
        }

        $parts = $query->get();

        $csvData = [];
        $csvData[] = ['Brand', 'Models', 'Part Name', 'Part Number', 'Description', 'Manufacturer', 'Category', 'Status', 'Created'];

        foreach ($parts as $part) {
            // Get all brands and models for this part
            $brands = $part->models->pluck('brand.name')->unique()->implode(', ');
            $models = $part->models->map(function ($model) {
                return $model->brand->name . ' ' . $model->name;
            })->implode(', ');

            $csvData[] = [
                $brands ?: '',
                $models ?: '',
                $part->name,
                $part->part_number ?: '',
                $part->description ?: '',
                $part->manufacturer ?: '',
                $part->category->name,
                $part->is_active ? 'Active' : 'Inactive',
                $part->created_at->format('Y-m-d H:i:s'),
            ];
        }

        $filename = 'parts_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Export single part to CSV with compatibility format.
     */
    public function exportSingle(Part $part)
    {
        // Check if export/import functionality is enabled
        $exportEnabled = \App\Models\SiteSetting::get('admin_parts_export_import_enabled', false);
        if (!$exportEnabled) {
            return response()->json(['error' => 'This feature is disabled for security reasons.'], 403);
        }
        $part->load(['category', 'models.brand']);

        // Get enabled columns from configuration
        $visibleColumns = $this->compatibilityColumnService->getVisibleColumns(true); // Admin mode to get all enabled columns

        $csvData = [];

        // Create header row from enabled columns
        $headers = ['ID']; // Always include ID for validation
        $columnKeys = ['id']; // Track column keys for data extraction

        foreach ($visibleColumns as $columnKey => $config) {
            $headers[] = $config['label'] ?? ucfirst(str_replace('_', ' ', $columnKey));
            $columnKeys[] = $columnKey;
        }
        $csvData[] = $headers;

        // Add each compatible model as a separate row
        foreach ($part->models as $model) {
            $row = [$part->id]; // Start with part ID

            foreach (array_slice($columnKeys, 1) as $columnKey) { // Skip 'id' as it's already added
                $config = $visibleColumns[$columnKey];
                $value = $this->compatibilityColumnService->getColumnValue($columnKey, $config, $model->toArray(), $part->toArray());

                // Handle boolean values for CSV
                if (is_bool($value)) {
                    $value = $value ? 'true' : 'false';
                }

                $row[] = $value;
            }

            $csvData[] = $row;
        }

        // If no compatible models, add a sample row for guidance
        if ($part->models->isEmpty()) {
            $sampleModel = [
                'id' => 0,
                'name' => 'iPhone 13',
                'model_number' => 'A2482',
                'brand' => ['name' => 'Apple'],
                'pivot' => [
                    'display_type' => 'OLED',
                    'display_size' => '6.1 inches',
                    'location' => 'Front',
                    'front_camera_type' => '12MP',
                    'camera_position' => 'Front',
                    'battery_mah' => '3095',
                    'pin_model' => 'Lightning',
                    'connector_types' => 'USB-C',
                    'types' => 'Premium',
                    'is_verified' => false,
                    'compatibility_notes' => 'Add your compatibility notes here',
                    'additional_info' => 'Additional information about compatibility'
                ]
            ];

            $row = [$part->id]; // Start with part ID

            foreach (array_slice($columnKeys, 1) as $columnKey) { // Skip 'id' as it's already added
                $config = $visibleColumns[$columnKey];
                $value = $this->compatibilityColumnService->getColumnValue($columnKey, $config, $sampleModel, $part->toArray());

                // Handle boolean values for CSV
                if (is_bool($value)) {
                    $value = $value ? 'true' : 'false';
                }

                $row[] = $value;
            }

            $csvData[] = $row;
        }

        $filename = 'part_compatibility_export_' . $part->id . '_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Download CSV template for parts import.
     */
    public function downloadTemplate()
    {
        // Check if export/import functionality is enabled
        $exportEnabled = \App\Models\SiteSetting::get('admin_parts_export_import_enabled', false);
        if (!$exportEnabled) {
            return response()->json(['error' => 'This feature is disabled for security reasons.'], 403);
        }
        $csvData = [];
        $csvData[] = ['Brand', 'Models', 'Part Name', 'Part Number', 'Description', 'Manufacturer', 'Category', 'Status', 'Created'];
        $csvData[] = ['# DUPLICATE DETECTION: Parts are considered duplicates if they have the same Part Number + Category, or Name + Category + Manufacturer'];
        $csvData[] = ['# IMPORT OPTIONS: Choose Skip (ignore duplicates), Update (overwrite existing), or Error (report as errors)'];
        $csvData[] = ['Apple', 'iPhone 12, iPhone 12 Pro', 'Battery', 'A2471', 'Lithium-ion battery for iPhone 12 series', 'Apple', 'Battery', 'Active', ''];
        $csvData[] = ['Samsung', 'Galaxy S21', 'Display', 'SM-G991B-DISPLAY', 'AMOLED display assembly', 'Samsung', 'Display', 'Active', ''];
        $csvData[] = ['Xiaomi', 'Redmi Note 9', 'Camera Module', 'IMX582', '48MP main camera sensor', 'Sony', 'Camera', 'Active', ''];

        $filename = 'parts_import_template.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Download CSV template for part compatibility import.
     */
    public function downloadCompatibilityTemplate(Part $part)
    {
        // Check if export/import functionality is enabled
        $exportEnabled = \App\Models\SiteSetting::get('admin_parts_export_import_enabled', false);
        if (!$exportEnabled) {
            return response()->json(['error' => 'This feature is disabled for security reasons.'], 403);
        }
        $part->load(['category']);

        // Get enabled columns from configuration
        $visibleColumns = $this->compatibilityColumnService->getVisibleColumns(true); // Admin mode to get all enabled columns

        $csvData = [];

        // Create header row from enabled columns
        $headers = ['ID']; // Always include ID for validation
        $columnKeys = ['id']; // Track column keys for data extraction

        foreach ($visibleColumns as $columnKey => $config) {
            $headers[] = $config['label'] ?? ucfirst(str_replace('_', ' ', $columnKey));
            $columnKeys[] = $columnKey;
        }
        $csvData[] = $headers;

        // Add sample compatible row
        $sampleCompatibleModel = [
            'id' => 0,
            'name' => 'iPhone 13',
            'model_number' => 'A2482',
            'brand' => ['name' => 'Apple'],
            'pivot' => [
                'display_type' => 'OLED',
                'display_size' => '6.1 inches',
                'location' => 'Front',
                'front_camera_type' => '12MP',
                'camera_position' => 'Front',
                'battery_mah' => '3095',
                'pin_model' => 'Lightning',
                'connector_types' => 'USB-C',
                'types' => 'Premium',
                'is_verified' => true,
                'is_compatible' => true,
                'compatibility_notes' => 'Fully tested and verified',
                'additional_info' => 'Works perfectly'
            ]
        ];

        $row = [$part->id]; // Start with part ID

        foreach (array_slice($columnKeys, 1) as $columnKey) { // Skip 'id' as it's already added
            $config = $visibleColumns[$columnKey];
            $value = $this->compatibilityColumnService->getColumnValue($columnKey, $config, $sampleCompatibleModel, $part->toArray());

            // Handle boolean values for CSV
            if (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            }

            $row[] = $value;
        }

        $csvData[] = $row;

        // Add sample partially compatible row
        $samplePartialModel = [
            'id' => 0,
            'name' => 'Galaxy S21',
            'model_number' => 'SM-G991B',
            'brand' => ['name' => 'Samsung'],
            'pivot' => [
                'display_type' => 'AMOLED',
                'display_size' => '6.2 inches',
                'location' => 'Front',
                'front_camera_type' => '10MP',
                'camera_position' => 'Front',
                'battery_mah' => '4000',
                'pin_model' => 'USB-C',
                'connector_types' => 'USB-C',
                'types' => 'Standard',
                'is_verified' => false,
                'is_compatible' => true,
                'compatibility_notes' => 'Compatible but needs verification',
                'additional_info' => 'Requires testing'
            ]
        ];

        $row = [$part->id]; // Start with part ID

        foreach (array_slice($columnKeys, 1) as $columnKey) { // Skip 'id' as it's already added
            $config = $visibleColumns[$columnKey];
            $value = $this->compatibilityColumnService->getColumnValue($columnKey, $config, $samplePartialModel, $part->toArray());

            // Handle boolean values for CSV
            if (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            }

            $row[] = $value;
        }

        $csvData[] = $row;

        // Add sample incompatible row
        $sampleIncompatibleModel = [
            'id' => 0,
            'name' => 'Redmi Note 9',
            'model_number' => 'M2003J15SC',
            'brand' => ['name' => 'Xiaomi'],
            'pivot' => [
                'display_type' => '',
                'display_size' => '',
                'location' => '',
                'front_camera_type' => '',
                'camera_position' => '',
                'battery_mah' => '',
                'pin_model' => '',
                'connector_types' => '',
                'types' => '',
                'is_verified' => false,
                'is_compatible' => false,
                'compatibility_notes' => 'Not compatible',
                'additional_info' => 'Incompatible design'
            ]
        ];

        $row = [$part->id]; // Start with part ID

        foreach (array_slice($columnKeys, 1) as $columnKey) { // Skip 'id' as it's already added
            $config = $visibleColumns[$columnKey];
            $value = $this->compatibilityColumnService->getColumnValue($columnKey, $config, $sampleIncompatibleModel, $part->toArray());

            // Handle boolean values for CSV
            if (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            }

            $row[] = $value;
        }

        $csvData[] = $row;

        $filename = 'compatibility_import_template_' . $part->id . '.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Preview CSV file for compatibility import.
     */
    public function previewCompatibilityImport(Request $request, Part $part)
    {
        // Check if export/import functionality is enabled
        $exportEnabled = \App\Models\SiteSetting::get('admin_parts_export_import_enabled', false);
        if (!$exportEnabled) {
            return response()->json(['error' => 'This feature is disabled for security reasons.'], 403);
        }
        $request->validate([
            'file' => 'required|file|mimes:csv,txt',
        ]);

        $file = $request->file('file');

        try {
            // Read CSV file with error handling
            $csvData = array_map(function($line) {
                return str_getcsv($line, ',', '"', '\\');
            }, file($file->path()));

            if (empty($csvData)) {
                return response()->json([
                    'success' => false,
                    'message' => 'The CSV file appears to be empty.'
                ], 400);
            }

            $header = array_shift($csvData);
            if (empty($header)) {
                return response()->json([
                    'success' => false,
                    'message' => 'The CSV file does not contain a valid header row.'
                ], 400);
            }

            // Clean up empty rows from all data first
            $cleanedData = array_filter($csvData, function($row) {
                return !empty(array_filter($row, function($cell) {
                    return !empty(trim($cell));
                }));
            });

            // Get preview rows (first 5 rows max) from cleaned data
            $previewRows = array_slice($cleanedData, 0, 5);

            // Map columns and detect their purpose
            $columns = [];
            $requiredColumns = ['Brand', 'Model', 'Compatible'];

            foreach ($header as $index => $columnName) {
                $columnName = trim($columnName);
                if (empty($columnName)) continue;

                // Get sample data for this column from cleaned data
                $sampleData = [];
                foreach ($cleanedData as $row) {
                    if (isset($row[$index]) && !empty(trim($row[$index]))) {
                        $sampleData[] = trim($row[$index]);
                    }
                }
                $sampleData = array_slice(array_unique($sampleData), 0, 3);

                // Detect column purpose and if it's required
                $mappedTo = $this->detectColumnMapping($columnName);
                $isRequired = in_array($mappedTo, $requiredColumns);

                $columns[] = [
                    'name' => $columnName,
                    'sampleData' => $sampleData,
                    'isRequired' => $isRequired,
                    'mappedTo' => $mappedTo,
                    'purpose' => $this->getColumnPurpose($mappedTo),
                    'isSelected' => true, // Default to selected
                ];
            }

            // Convert preview rows to associative arrays
            $previewData = [];
            foreach ($previewRows as $row) {
                $rowData = [];
                foreach ($header as $index => $columnName) {
                    $rowData[trim($columnName)] = isset($row[$index]) ? trim($row[$index]) : '';
                }
                $previewData[] = $rowData;
            }

            return response()->json([
                'success' => true,
                'columns' => $columns,
                'totalRows' => count($cleanedData),
                'previewRows' => $previewData,
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error reading CSV file: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Detect what field a CSV column maps to.
     */
    private function detectColumnMapping(string $columnName): string
    {
        $columnName = strtolower(trim($columnName));

        // ID mapping
        if (in_array($columnName, ['id', 'part id', 'part_id', 'record id'])) {
            return 'ID';
        }

        // Brand mapping
        if (in_array($columnName, ['brand', 'brand name', 'manufacturer brand'])) {
            return 'Brand';
        }

        // Model mapping
        if (in_array($columnName, ['model', 'model name', 'device model', 'phone model'])) {
            return 'Model';
        }

        // Model Number mapping
        if (in_array($columnName, ['model number', 'model_number', 'device number'])) {
            return 'Model Number';
        }

        // Compatible mapping
        if (in_array($columnName, ['compatible', 'is compatible', 'compatibility', 'is_compatible'])) {
            return 'Compatible';
        }

        // Verified mapping
        if (in_array($columnName, ['verified', 'is verified', 'is_verified', 'verification'])) {
            return 'Verified';
        }

        // Display Type mapping
        if (in_array($columnName, ['display type', 'display types', 'display_type', 'screen type', 'panel type'])) {
            return 'Display Type';
        }

        // Display Size mapping
        if (in_array($columnName, ['display size', 'display_size', 'screen size', 'size'])) {
            return 'Display Size';
        }

        // Location mapping
        if (in_array($columnName, ['location', 'position', 'placement'])) {
            return 'Location';
        }

        // Notes mapping
        if (in_array($columnName, ['notes', 'note', 'comments', 'comment', 'compatibility notes', 'compatibility_notes'])) {
            return 'Notes';
        }

        // Part Name mapping
        if (in_array($columnName, ['part name', 'part_name', 'parts name', 'component name'])) {
            return 'Part Name';
        }

        // Part Number mapping
        if (in_array($columnName, ['part number', 'part_number', 'component number'])) {
            return 'Part Number';
        }

        // Description mapping
        if (in_array($columnName, ['description', 'desc', 'details', 'part description'])) {
            return 'Description';
        }

        // Manufacturer mapping
        if (in_array($columnName, ['manufacturer', 'mfg', 'maker', 'part manufacturer'])) {
            return 'Manufacturer';
        }

        // Category mapping
        if (in_array($columnName, ['category', 'type', 'part type'])) {
            return 'Category';
        }

        // Front Camera Type mapping
        if (in_array($columnName, ['front camera type', 'front_camera_type', 'camera type'])) {
            return 'Front Camera Type';
        }

        // Camera Position mapping
        if (in_array($columnName, ['camera position', 'camera_position', 'cam position'])) {
            return 'Camera Position';
        }

        // Battery mAh mapping
        if (in_array($columnName, ['battery mah', 'battery_mah', 'battery capacity', 'mah'])) {
            return 'Battery mAh';
        }

        // Pin & Model mapping
        if (in_array($columnName, ['pin & model', 'pin model', 'pin_model', 'connector model'])) {
            return 'Pin & Model';
        }

        // Connector Types mapping
        if (in_array($columnName, ['connector types', 'connector_types', 'connectors', 'connection types'])) {
            return 'Connector Types';
        }

        // Types mapping
        if (in_array($columnName, ['types', 'part types', 'component types'])) {
            return 'Types';
        }

        // Additional Info mapping
        if (in_array($columnName, ['additional info', 'additional_info', 'extra info', 'additional information'])) {
            return 'Additional Info';
        }

        // Return original name if no mapping found
        return ucwords(str_replace(['_', '-'], ' ', $columnName));
    }

    /**
     * Get human-readable purpose for a column.
     */
    private function getColumnPurpose(string $mappedTo): string
    {
        $purposes = [
            'ID' => 'Part identifier',
            'Brand' => 'Device manufacturer',
            'Model' => 'Device model name',
            'Model Number' => 'Device model number',
            'Compatible' => 'Compatibility status',
            'Verified' => 'Verification status',
            'Display Type' => 'Display technology',
            'Display Size' => 'Screen size',
            'Location' => 'Part location',
            'Notes' => 'Additional notes',
            'Part Name' => 'Part name (reference)',
            'Part Number' => 'Part number (reference)',
            'Description' => 'Part description (reference)',
            'Manufacturer' => 'Part manufacturer (reference)',
            'Category' => 'Part category (reference)',
            'Front Camera Type' => 'Front camera technology',
            'Camera Position' => 'Camera placement location',
            'Battery mAh' => 'Battery capacity',
            'Pin & Model' => 'Connector pin configuration',
            'Connector Types' => 'Connection interface types',
            'Types' => 'Component type classification',
            'Additional Info' => 'Extra compatibility details',
        ];

        return $purposes[$mappedTo] ?? 'Additional data';
    }

    /**
     * Import compatibility data for a specific part from CSV.
     */
    public function importCompatibility(Request $request, Part $part)
    {
        // Check if export/import functionality is enabled
        $exportEnabled = \App\Models\SiteSetting::get('admin_parts_export_import_enabled', false);
        if (!$exportEnabled) {
            return response()->json(['error' => 'This feature is disabled for security reasons.'], 403);
        }
        \Log::info('Compatibility import started', ['part_id' => $part->id, 'part_name' => $part->name]);

        $request->validate([
            'file' => 'required|file|mimes:csv,txt',
            'selected_columns' => 'nullable|array',
            'selected_columns.*' => 'string',
        ]);

        $file = $request->file('file');
        $selectedColumns = $request->input('selected_columns', []);
        \Log::info('File received', [
            'filename' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'selected_columns' => $selectedColumns
        ]);

        // Read and parse CSV file with error handling
        try {
            $csvData = array_map(function($line) {
                return str_getcsv($line, ',', '"', '\\');
            }, file($file->path()));
            if (empty($csvData)) {
                \Log::error('CSV file is empty');
                return back()->withErrors(['file' => 'The CSV file appears to be empty.']);
            }
            $header = array_shift($csvData);
            if (empty($header)) {
                \Log::error('CSV file has no header');
                return back()->withErrors(['file' => 'The CSV file does not contain a valid header row.']);
            }
            \Log::info('CSV parsed successfully', ['header' => $header, 'rows_count' => count($csvData)]);

            // Filter header and data based on selected columns (if provided)
            if (!empty($selectedColumns)) {
                $selectedIndexes = [];
                $filteredHeader = [];

                foreach ($header as $index => $columnName) {
                    if (in_array(trim($columnName), $selectedColumns)) {
                        $selectedIndexes[] = $index;
                        $filteredHeader[] = $columnName;
                    }
                }

                // Filter CSV data to only include selected columns
                $filteredCsvData = [];
                foreach ($csvData as $row) {
                    $filteredRow = [];
                    foreach ($selectedIndexes as $index) {
                        $filteredRow[] = isset($row[$index]) ? $row[$index] : '';
                    }
                    $filteredCsvData[] = $filteredRow;
                }

                $header = $filteredHeader;
                $csvData = $filteredCsvData;

                \Log::info('Filtered CSV data', ['filtered_header' => $header, 'selected_columns' => $selectedColumns]);
            }

            // Validate CSV format by checking required headers
            $requiredHeaders = ['Brand', 'Model', 'Compatible'];
            $mappedHeaders = array_map([$this, 'detectColumnMapping'], $header);
            $missingHeaders = array_diff($requiredHeaders, $mappedHeaders);

            if (!empty($missingHeaders)) {
                \Log::error('Invalid CSV format - missing headers', ['missing' => $missingHeaders, 'provided' => $header, 'mapped' => $mappedHeaders]);
                return back()->withErrors(['file' => 'Invalid CSV format. Missing required columns: ' . implode(', ', $missingHeaders) . '. Please ensure you have selected the Brand, Model, and Compatible columns.']);
            }
        } catch (Exception $e) {
            \Log::error('Error reading CSV file', ['error' => $e->getMessage()]);
            return back()->withErrors(['file' => 'Error reading CSV file: ' . $e->getMessage()]);
        }

        $imported = 0;
        $updated = 0;
        $partUpdated = false;
        $errors = [];
        $warnings = [];
        $prefixRemovals = [];

        \Log::info('Starting transaction for compatibility import');

        DB::transaction(function () use ($csvData, $header, $part, &$imported, &$updated, &$partUpdated, &$errors, &$warnings, &$prefixRemovals) {
            \Log::info('Inside transaction', ['rows_to_process' => count($csvData)]);

            foreach ($csvData as $index => $row) {
                \Log::info('Processing row', ['index' => $index, 'row' => $row]);

                // Skip empty rows or comment rows (starting with #)
                if (empty($row) || (isset($row[0]) && strpos(trim($row[0]), '#') === 0)) {
                    \Log::info('Skipping row', ['reason' => 'empty or comment', 'index' => $index]);
                    continue;
                }

                // Ensure row has the same number of columns as header
                $headerCount = count($header);
                $rowCount = count($row);

                if ($rowCount < $headerCount) {
                    // Pad with empty strings
                    $row = array_pad($row, $headerCount, '');
                } elseif ($rowCount > $headerCount) {
                    // Trim extra columns
                    $row = array_slice($row, 0, $headerCount);
                }

                $data = array_combine($header, $row);

                // Validate ID if present (for enhanced CSV format)
                if (isset($data['ID']) && !empty($data['ID'])) {
                    if ($data['ID'] != $part->id) {
                        $errors[] = "Row " . ($index + 1) . ": ID {$data['ID']} does not match part ID {$part->id}. Skipping row.";
                        \Log::warning('ID mismatch in CSV import', [
                            'row_index' => $index + 1,
                            'csv_id' => $data['ID'],
                            'part_id' => $part->id
                        ]);
                        continue;
                    }
                }

                // Map CSV headers to expected format using dynamic column detection
                $mappedData = [];
                foreach ($header as $columnName) {
                    $mappedTo = $this->detectColumnMapping($columnName);
                    $value = $data[$columnName] ?? null;

                    switch ($mappedTo) {
                        case 'Brand':
                            $mappedData['brand'] = $value;
                            break;
                        case 'Model':
                            $mappedData['model'] = $value;
                            break;
                        case 'Model Number':
                            $mappedData['model_number'] = $value;
                            break;
                        case 'Compatible':
                            $mappedData['compatible'] = $value;
                            break;
                        case 'Verified':
                            $mappedData['verified'] = $value;
                            break;
                        case 'Display Type':
                            $mappedData['display_type'] = $value;
                            break;
                        case 'Display Size':
                            $mappedData['display_size'] = $value;
                            break;
                        case 'Location':
                            $mappedData['location'] = $value;
                            break;
                        case 'Notes':
                            $mappedData['notes'] = $value;
                            break;
                        case 'Front Camera Type':
                            $mappedData['front_camera_type'] = $value;
                            break;
                        case 'Camera Position':
                            $mappedData['camera_position'] = $value;
                            break;
                        case 'Battery mAh':
                            $mappedData['battery_mah'] = $value;
                            break;
                        case 'Pin & Model':
                            $mappedData['pin_model'] = $value;
                            break;
                        case 'Connector Types':
                            $mappedData['connector_types'] = $value;
                            break;
                        case 'Types':
                            $mappedData['types'] = $value;
                            break;
                        case 'Additional Info':
                            $mappedData['additional_info'] = $value;
                            break;
                        case 'Part Name':
                            $mappedData['part_name'] = $value;
                            break;
                        case 'Part Number':
                            $mappedData['part_number'] = $value;
                            break;
                        case 'Description':
                            $mappedData['description'] = $value;
                            break;
                        case 'Manufacturer':
                            $mappedData['manufacturer'] = $value;
                            break;
                        case 'Category':
                            $mappedData['category'] = $value;
                            break;
                    }
                }

                // Apply consistent trimming to all mapped data
                $mappedData = $this->trimCsvData($mappedData);

                // Update part information if present in CSV (enhanced compatibility import)
                $partUpdateData = [];
                $hasPartData = false;

                if (!empty($mappedData['part_name'])) {
                    if (strlen($mappedData['part_name']) <= 255) {
                        $partUpdateData['name'] = $mappedData['part_name'];
                        $hasPartData = true;
                    } else {
                        $warnings[] = "Row " . ($index + 2) . ": Part name is too long (max 255 characters). Skipping part name update.";
                    }
                }

                if (!empty($mappedData['part_number'])) {
                    if (strlen($mappedData['part_number']) <= 255) {
                        $partUpdateData['part_number'] = $mappedData['part_number'];
                        $hasPartData = true;
                    } else {
                        $warnings[] = "Row " . ($index + 2) . ": Part number is too long (max 255 characters). Skipping part number update.";
                    }
                }

                if (!empty($mappedData['description']) && trim($mappedData['description']) !== '') {
                    $cleanDescription = trim($mappedData['description']);
                    // Description can be longer as it's a text field
                    $partUpdateData['description'] = $cleanDescription;
                    $hasPartData = true;
                }

                if (!empty($mappedData['manufacturer']) && trim($mappedData['manufacturer']) !== '') {
                    $cleanManufacturer = trim($mappedData['manufacturer']);
                    if (strlen($cleanManufacturer) <= 255) {
                        $partUpdateData['manufacturer'] = $cleanManufacturer;
                        $hasPartData = true;
                    } else {
                        $warnings[] = "Row " . ($index + 2) . ": Manufacturer name is too long (max 255 characters). Skipping manufacturer update.";
                    }
                }

                // Update part if we have part data and this is the first row being processed
                if ($hasPartData && $index === 0) {
                    try {
                        // Validate part update data before applying
                        $partValidator = Validator::make($partUpdateData, [
                            'name' => 'nullable|string|max:255',
                            'part_number' => 'nullable|string|max:255',
                            'description' => 'nullable|string',
                            'manufacturer' => 'nullable|string|max:255',
                        ]);

                        if ($partValidator->fails()) {
                            $errors[] = "Row " . ($index + 2) . ": Part data validation failed: " . implode(', ', $partValidator->errors()->all());
                        } else {
                            // Store original values for logging
                            $originalValues = [
                                'name' => $part->name,
                                'part_number' => $part->part_number,
                                'description' => $part->description,
                                'manufacturer' => $part->manufacturer,
                            ];

                            $part->update($partUpdateData);
                            $partUpdated = true;

                            // Log detailed update information
                            $changedFields = [];
                            foreach ($partUpdateData as $field => $newValue) {
                                $oldValue = $originalValues[$field] ?? null;
                                if ($oldValue !== $newValue) {
                                    $changedFields[$field] = [
                                        'old' => $oldValue,
                                        'new' => $newValue
                                    ];
                                }
                            }

                            \Log::info('Updated part information from CSV', [
                                'part_id' => $part->id,
                                'part_name' => $part->name,
                                'row_index' => $index + 2,
                                'changed_fields' => $changedFields,
                                'total_fields_updated' => count($partUpdateData)
                            ]);
                        }
                    } catch (Exception $e) {
                        \Log::error('Failed to update part information', [
                            'part_id' => $part->id,
                            'part_name' => $part->name,
                            'row_index' => $index + 2,
                            'error' => $e->getMessage(),
                            'error_code' => $e->getCode(),
                            'data' => $partUpdateData
                        ]);
                        $errors[] = "Row " . ($index + 2) . ": Failed to update part information: " . $e->getMessage();
                    }
                } elseif ($hasPartData && $index > 0) {
                    // Log that part data was found in subsequent rows but not processed
                    \Log::info('Part data found in non-first row, skipping part update', [
                        'part_id' => $part->id,
                        'row_index' => $index + 2,
                        'part_data_fields' => array_keys($partUpdateData)
                    ]);
                }

                $validator = Validator::make($mappedData, [
                    'brand' => 'required|string',
                    'model' => 'required|string',
                    'model_number' => 'nullable|string',
                    'compatible' => 'required|string',
                    'verified' => 'nullable|string',
                    'display_type' => 'nullable|string|max:255',
                    'display_size' => 'nullable|string|max:255',
                    'location' => 'nullable|string|max:255',
                    'front_camera_type' => 'nullable|string|max:255',
                    'camera_position' => 'nullable|string|max:255',
                    'battery_mah' => 'nullable|string|max:255',
                    'pin_model' => 'nullable|string|max:255',
                    'connector_types' => 'nullable|string|max:255',
                    'types' => 'nullable|string|max:255',
                    'notes' => 'nullable|string',
                    'additional_info' => 'nullable|string',
                    'part_name' => 'nullable|string|max:255',
                    'part_number' => 'nullable|string|max:255',
                    'description' => 'nullable|string',
                    'manufacturer' => 'nullable|string|max:255',
                    'category' => 'nullable|string|max:255',
                ]);

                if ($validator->fails()) {
                    $errors[] = "Row " . ($index + 2) . ": " . implode(', ', $validator->errors()->all());
                    continue;
                }

                // Add data quality warnings
                if (!empty($mappedData['model']) && stripos($mappedData['model'], $mappedData['brand']) === 0) {
                    $warnings[] = "Row " . ($index + 2) . ": Model name '{$mappedData['model']}' appears to contain brand prefix. This will be automatically cleaned for matching.";
                }

                // Find the brand
                $brand = Brand::where('name', $mappedData['brand'])->first();
                if (!$brand) {
                    $errors[] = "Row " . ($index + 2) . ": Brand '{$mappedData['brand']}' not found";
                    continue;
                }

                // Find the model - try exact match first, then try without brand prefix
                $modelQuery = MobileModel::where('brand_id', $brand->id)
                    ->where('name', $mappedData['model']);

                if (!empty($mappedData['model_number'])) {
                    $modelQuery->where('model_number', $mappedData['model_number']);
                }

                $model = $modelQuery->first();

                // If not found, try intelligent brand prefix removal
                if (!$model) {
                    $modelNameWithoutBrand = null;
                    $prefixRemovalMethod = null;

                    // Method 1: Exact brand prefix with space (most common case)
                    if (stripos($mappedData['model'], $mappedData['brand'] . ' ') === 0) {
                        $modelNameWithoutBrand = trim(substr($mappedData['model'], strlen($mappedData['brand'] . ' ')));
                        $prefixRemovalMethod = 'exact_with_space';
                    }
                    // Method 2: Exact brand prefix without space (fallback)
                    elseif (stripos($mappedData['model'], $mappedData['brand']) === 0 &&
                            strlen($mappedData['model']) > strlen($mappedData['brand'])) {
                        $modelNameWithoutBrand = trim(substr($mappedData['model'], strlen($mappedData['brand'])));
                        $prefixRemovalMethod = 'exact_without_space';
                    }

                    if ($modelNameWithoutBrand && !empty($modelNameWithoutBrand)) {
                        $modelQuery = MobileModel::where('brand_id', $brand->id)
                            ->where('name', $modelNameWithoutBrand);

                        if (!empty($mappedData['model_number'])) {
                            $modelQuery->where('model_number', $mappedData['model_number']);
                        }

                        $model = $modelQuery->first();

                        if ($model) {
                            // Track successful prefix removal for user feedback
                            $prefixRemovals[] = [
                                'row' => $index + 2,
                                'original' => $mappedData['model'],
                                'cleaned' => $modelNameWithoutBrand,
                                'method' => $prefixRemovalMethod,
                                'brand' => $mappedData['brand']
                            ];

                            \Log::info('Model found after removing brand prefix', [
                                'row' => $index + 2,
                                'original_model_name' => $mappedData['model'],
                                'cleaned_model_name' => $modelNameWithoutBrand,
                                'found_model_id' => $model->id,
                                'removal_method' => $prefixRemovalMethod
                            ]);
                        } else {
                            // Log failed prefix removal attempt
                            \Log::info('Brand prefix removal attempted but model still not found', [
                                'row' => $index + 2,
                                'original_model_name' => $mappedData['model'],
                                'cleaned_model_name' => $modelNameWithoutBrand,
                                'brand' => $mappedData['brand'],
                                'removal_method' => $prefixRemovalMethod
                            ]);
                        }
                    }
                }

                if (!$model) {
                    $errors[] = "Row " . ($index + 2) . ": Model '{$mappedData['brand']} {$mappedData['model']}' not found";
                    continue;
                }

                // Parse compatibility status
                $isCompatible = in_array(strtolower(trim($mappedData['compatible'])), ['true', '1', 'yes', 'compatible']);
                $isVerified = in_array(strtolower(trim($mappedData['verified'] ?? 'false')), ['true', '1', 'yes', 'verified']);

                \Log::info('Compatibility status parsed', [
                    'model_id' => $model->id,
                    'model_name' => $model->name,
                    'is_compatible' => $isCompatible,
                    'is_verified' => $isVerified,
                    'raw_compatible' => $mappedData['compatible'],
                    'raw_verified' => $mappedData['verified'] ?? 'false'
                ]);

                // Check if compatibility already exists
                $existingCompatibility = $part->models()->where('models.id', $model->id)->exists();
                \Log::info('Existing compatibility check', ['model_id' => $model->id, 'exists' => $existingCompatibility]);

                // Always create/update relationship, but set is_compatible appropriately
                $pivotData = [
                    'compatibility_notes' => $mappedData['notes'] ?? null,
                    'is_verified' => $isVerified,
                    'is_compatible' => $isCompatible,
                    'display_type' => $mappedData['display_type'] ?? null,
                    'display_size' => $mappedData['display_size'] ?? null,
                    'location' => $mappedData['location'] ?? null,
                    'front_camera_type' => $mappedData['front_camera_type'] ?? null,
                    'camera_position' => $mappedData['camera_position'] ?? null,
                    'battery_mah' => $mappedData['battery_mah'] ?? null,
                    'pin_model' => $mappedData['pin_model'] ?? null,
                    'connector_types' => $mappedData['connector_types'] ?? null,
                    'types' => $mappedData['types'] ?? null,
                    'additional_info' => $mappedData['additional_info'] ?? null,
                ];

                if ($existingCompatibility) {
                    // Update existing compatibility
                    \Log::info('Updating existing compatibility', [
                        'model_id' => $model->id,
                        'is_compatible' => $isCompatible
                    ]);
                    $part->models()->updateExistingPivot($model->id, $pivotData);
                    $updated++;
                    \Log::info('Updated compatibility', ['model_id' => $model->id, 'updated_count' => $updated]);
                } else {
                    // Add new compatibility (both compatible and incompatible items)
                    \Log::info('Adding new compatibility', [
                        'model_id' => $model->id,
                        'is_compatible' => $isCompatible
                    ]);
                    $part->models()->attach($model->id, $pivotData);
                    $imported++;
                    \Log::info('Added compatibility', ['model_id' => $model->id, 'imported_count' => $imported]);
                }
            }
        });

        // Build comprehensive success message
        $messageParts = [];
        if ($imported > 0) $messageParts[] = "Added: {$imported}";
        if ($updated > 0) $messageParts[] = "Updated: {$updated}";

        \Log::info('Import completed', [
            'imported' => $imported,
            'updated' => $updated,
            'errors_count' => count($errors),
            'prefix_removals_count' => count($prefixRemovals),
        ]);

        // Build comprehensive success message with prefix removal feedback
        $message = '';
        $hasOperations = !empty($messageParts);
        $hasErrors = !empty($errors);
        $hasPrefixRemovals = !empty($prefixRemovals);

        if ($hasErrors && !$hasOperations) {
            // Only errors, no successful operations
            return back()->withErrors(['file' => 'Import failed. ' . implode(' ', $errors)]);
        }

        if ($hasOperations) {
            $message = "Compatibility import completed successfully. " . implode(', ', $messageParts) . " compatibility records.";
            if ($partUpdated) {
                $message .= " Part information was also updated from the CSV data.";
            }
        } else {
            if ($partUpdated) {
                $message = "Part information was updated from the CSV data. No compatibility changes were made.";
            } else {
                $message = "No changes were made. Please check your CSV file format.";
            }
        }

        // Add prefix removal feedback
        if ($hasPrefixRemovals) {
            $prefixMessage = " Note: Brand prefixes were automatically removed from " . count($prefixRemovals) . " model name(s) for better matching:";
            foreach ($prefixRemovals as $removal) {
                $prefixMessage .= " Row {$removal['row']}: '{$removal['original']}' → '{$removal['cleaned']}'.";
            }
            $message .= $prefixMessage;
        }

        // Auto-enable columns that had meaningful data imported
        if ($hasOperations) {
            $importedColumns = [];
            foreach ($header as $columnName) {
                $mappedTo = $this->detectColumnMapping($columnName);

                // Skip core columns that are always handled
                if (in_array($mappedTo, ['ID', 'Brand', 'Model', 'Compatible'])) {
                    continue;
                }

                // Check if this column actually has meaningful data in the imported rows
                $hasData = false;
                $columnIndex = array_search($columnName, $header);
                if ($columnIndex !== false) {
                    foreach ($csvData as $row) {
                        if (isset($row[$columnIndex])) {
                            $value = trim($row[$columnIndex]);
                            // Consider data meaningful if it's not empty and not just "-" or "N/A"
                            if (!empty($value) && !in_array(strtolower($value), ['-', 'n/a', 'na', 'null', 'none', ''])) {
                                $hasData = true;
                                break;
                            }
                        }
                    }
                }

                if ($hasData) {
                    // Map display names to config keys
                    $configKeyMapping = [
                        'Model Number' => 'model_number',
                        'Part Name' => 'part_name',
                        'Part Number' => 'part_number',
                        'Description' => 'description',
                        'Manufacturer' => 'manufacturer',
                        'Category' => 'category',
                        'Display Type' => 'display_type',
                        'Display Size' => 'display_size',
                        'Location' => 'location',
                        'Front Camera Type' => 'front_camera_type',
                        'Camera Position' => 'camera_position',
                        'Battery mAh' => 'battery_mah',
                        'Pin & Model' => 'pin_model',
                        'Connector Types' => 'connector_types',
                        'Types' => 'types',
                        'Notes' => 'notes',
                        'Additional Info' => 'additional_info',
                    ];

                    if (isset($configKeyMapping[$mappedTo])) {
                        $importedColumns[] = $configKeyMapping[$mappedTo];
                    }
                }
            }

            if (!empty($importedColumns)) {
                $this->compatibilityColumnService->autoEnableColumnsWithData($importedColumns);
                \Log::info('Auto-enabled columns with meaningful data', ['columns' => $importedColumns]);
            }
        }

        // Add errors as warnings if there were successful operations
        if ($hasErrors && $hasOperations) {
            $message .= " However, there were some issues: " . implode(' ', $errors);
        }

        // Add warnings if any
        if (!empty($warnings)) {
            if ($hasOperations || $partUpdated) {
                $message .= " Warnings: " . implode(' ', $warnings);
            } else {
                $message = "Import completed with warnings: " . implode(' ', $warnings);
            }
        }

        return back()->with('success', $message);
    }

    /**
     * Get compatibility columns configuration.
     */
    public function getCompatibilityColumnsConfig()
    {
        try {
            $config = $this->compatibilityColumnService->getColumnConfiguration(true);

            return response()->json([
                'success' => true,
                'data' => $config
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve column configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update compatibility columns configuration.
     */
    public function updateCompatibilityColumnsConfig(Request $request)
    {
        try {
            $request->validate([
                'columns' => 'required|array',
                'columns.*.enabled' => 'required|boolean',
                'columns.*.required' => 'sometimes|boolean',
                'columns.*.order' => 'sometimes|integer|min:1',
                'columns.*.label' => 'sometimes|string|max:255',
            ]);

            $columns = $request->input('columns');

            // Validate the configuration
            $errors = $this->compatibilityColumnService->validateConfiguration($columns);
            if (!empty($errors)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Configuration validation failed',
                    'errors' => $errors
                ], 422);
            }

            // Update the configuration
            $result = $this->compatibilityColumnService->updateColumnConfiguration($columns);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Column configuration updated successfully',
                    'data' => $this->compatibilityColumnService->getColumnConfiguration(true)
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update column configuration'
                ], 500);
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update column configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
